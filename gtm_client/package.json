{"name": "gtm-client", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev --host 0.0.0.0 --port 3000", "dev:mastra": "<PERSON>ra dev", "build": "vite build", "build:mastra": "mastra build", "start": "node build/index.js", "preview": "vite preview --host 0.0.0.0 --port 3000", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test": "npm run test:unit -- --run", "circular-dependency-check": "dpdm --no-warning --no-tree --exit-code circular:1 src/**/*.svelte src/**/*.js src/**/*.ts", "machine-translate": "inlang machine translate --project project.inlang"}, "dependencies": {"@ai-sdk/google": "^2.0.17", "@ai-sdk/openai": "^2.0.42", "@mastra/core": "^0.20.0", "@mastra/libsql": "^0.15.1", "@mastra/memory": "^0.15.5", "@sveltejs/adapter-node": "^5.3.3", "@sveltejs/kit": "^2.44.0", "cookie": "^1.0.2", "js-cookie": "^3.0.5", "mastra": "^0.14.1", "set-cookie-parser": "^2.7.1"}, "pnpm": {"overrides": {"zod": "^3.23.8"}}, "devDependencies": {"@atlaskit/pragmatic-drag-and-drop": "^1.7.7", "@eslint/compat": "^1.4.0", "@eslint/js": "^9.37.0", "@inlang/cli": "^3.0.12", "@inlang/paraglide-js": "2.4.0", "@internationalized/date": "^3.10.0", "@lucide/svelte": "^0.544.0", "@midudev/tailwind-animations": "^0.2.0", "@paralleldrive/cuid2": "^2.2.2", "@sveltejs/vite-plugin-svelte": "^6.2.1", "@sveltelegos-blue/svelte-legos": "^0.5.1", "@tailwindcss/postcss": "^4.1.14", "@tailwindcss/typography": "^0.5.19", "@tailwindcss/vite": "^4.1.14", "@testing-library/jest-dom": "^6.9.1", "@testing-library/svelte": "^5.2.8", "@types/set-cookie-parser": "^2.4.10", "adapter": "link:@atlaskit/pragmatic-drag-and-drop/element/adapter", "bits-ui": "^2.11.4", "clsx": "^2.1.1", "daisyui": "^5.1.27", "dayjs": "^1.11.18", "dpdm": "^3.14.0", "eslint": "^9.37.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-svelte": "^3.12.4", "formsnap": "^2.0.1", "globals": "^16.4.0", "js-cookie": "^3.0.5", "jsdom": "^27.0.0", "lint-staged": "^16.2.3", "mode-watcher": "1.1.0", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "runed": "^0.34.0", "svelte": "^5.39.9", "svelte-check": "^4.3.2", "svelte-sonner": "^1.0.5", "sveltekit-superforms": "^2.27.2", "tailwind-merge": "^3.3.1", "tailwind-scrollbar": "^4.0.2", "tailwind-variants": "^3.1.1", "tailwindcss": "^4.1.14", "tw-animate-css": "^1.4.0", "typescript": "^5.9.3", "typescript-eslint": "^8.45.0", "vaul-svelte": "1.0.0-next.7", "vite": "^7.1.9", "vitest": "^3.2.4", "zod": "3.23.8"}}
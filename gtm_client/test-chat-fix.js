/**
 * Test script to verify chat fixes
 */

const testContext = {
    userId: "1",
    userEmail: "<EMAIL>",
    userInformation: {
        id: 1,
        email: "<EMAIL>",
        first_name: "<PERSON><PERSON>",
        last_name: "<PERSON><PERSON>"
    },
    currentOrganizationId: "d926f188-407e-43b7-b365-120ba50f0712",
    currentOrganizationInformation: {
        id: "d926f188-407e-43b7-b365-120ba50f0712",
        name: "<PERSON><PERSON><PERSON><PERSON>'s Personal Account",
        owner: {
            email: "<EMAIL>",
            first_name: "<PERSON><PERSON><PERSON><PERSON>",
            id: 1,
            last_name: "<PERSON>"
        }
    },
    currentWorkspaceId: "",
    currentWorkspaceInformation: {},
    currentProjectId: "",
    currentProjectInformation: {}
};

async function testChat() {
    try {
        console.log('Testing chat endpoint...');
        
        const response = await fetch('http://localhost:5173/api/mastra/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: "Hi, can you help me create a workspace?",
                messages: [],
                stream: false,
                context: testContext
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('Chat response:', JSON.stringify(result, null, 2));
        
        // Check if result.result is a string (not [object Object])
        if (typeof result.result === 'string') {
            console.log('✅ Text response is properly formatted');
        } else {
            console.log('❌ Text response is not a string:', typeof result.result);
        }
        
        // Check if toolCalls are present
        if (result.toolCalls && Array.isArray(result.toolCalls)) {
            console.log('✅ Tool calls are present:', result.toolCalls.length);
        } else {
            console.log('ℹ️ No tool calls in response');
        }
        
        // Check if uiActions are present
        if (result.uiActions && Array.isArray(result.uiActions)) {
            console.log('✅ UI actions are present:', result.uiActions.length);
        } else {
            console.log('ℹ️ No UI actions in response');
        }

    } catch (error) {
        console.error('Test failed:', error);
    }
}

// Run the test
testChat();

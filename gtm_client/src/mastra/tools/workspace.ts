import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { post<PERSON><PERSON>, get<PERSON><PERSON>, put<PERSON><PERSON> } from '$mastra/lib/backend-client';
import { workspaceOutputSchema } from '$mastra/lib/schemas';
import { normalizeWorkspacePayload } from '$mastra/lib/tool-helpers';

/**
 * Create a new workspace
 */
export const createWorkspaceTool = createTool({
	id: 'create-workspace',
	description:
		'Create a new workspace in the organization. The organization ID will be automatically taken from the Runtime context.',
	inputSchema: z.object({
		title: z.string().min(3).max(50).describe('The workspace title (3-50 characters)'),
		description: z.string().optional().describe('Optional workspace description')
	}),
	outputSchema: workspaceOutputSchema.optional(),
	execute: async ({ context, runtimeContext }) => {
		try {
			const { title, description = '' } = context;

			const payload: any = { title };
			if (description) payload.description = description;

			const currentOrganizationId = runtimeContext.get('currentOrganizationId') as string;

			// Use organization_id from parameter or user context
			const orgId = currentOrganizationId;

			if (orgId) {
				payload.organization_id = orgId;
			} else {
				return {
					success: false,
					status: 'error',
					error: 'Organization ID is required but not found in context'
				};
			}

			const result = await postJson<any>('/api/v1/workspaces/new/', payload, user_context);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				const workspace = normalized.workspace || normalized;

				return {
					success: true,
					status: 'success',
					workspace: workspace,
					ui_actions: [
						{
							type: 'navigate',
							label: `Go to ${workspace?.title || workspace?.name || 'workspace'}`,
							action: 'navigate',
							data: {
								url: `/workspaces/${workspace?.id}`,
								workspace_id: workspace?.id,
								workspace_title: workspace?.title || workspace?.name
							}
						}
					]
				};
			} else {
				return {
					success: false,
					status: 'error',
					error: result.error
				};
			}
		} catch (error) {
			return {
				success: false,
				status: 'error',
				error: error instanceof Error ? error.message : 'Unknown error'
			};
		}
	}
});

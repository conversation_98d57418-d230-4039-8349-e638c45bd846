import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { post<PERSON><PERSON>, get<PERSON><PERSON>, put<PERSON><PERSON> } from '$mastra/lib/backend-client';
import { workspaceOutputSchema } from '$mastra/lib/schemas';
import { normalizeWorkspacePayload } from '$mastra/lib/tool-helpers';

/**
 * Create a new workspace
 */
export const createWorkspaceTool = createTool({
	id: 'create-workspace',
	description:
		'Create a new workspace in the organization. The organization ID will be automatically taken from the Runtime context.',
	inputSchema: z.object({
		title: z.string().min(3).max(50).describe('The workspace title (3-50 characters)'),
		description: z.string().optional().describe('Optional workspace description')
	}),
	outputSchema: workspaceOutputSchema.optional(),
	execute: async ({ context, runtimeContext }) => {
		try {
			const { title, description = '' } = context;

			const payload: any = { title };
			if (description) payload.description = description;

			const currentOrganizationId = runtimeContext.get('currentOrganizationId') as string;

			// Use organization_id from parameter or user context
			const orgId = currentOrganizationId;

			if (orgId) {
				payload.organization_id = orgId;
			} else {
				return {
					success: false,
					status: 'error',
					error: 'Organization ID is required but not found in context'
				};
			}

			const result = await postJson<any>(
				'/api/v1/workspaces/new/',
				payload,
				runtimeContext.toJSON()
			);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				const workspace = normalized.workspace || normalized;

				return {
					success: true,
					status: 'success',
					workspace: workspace,
					ui_actions: [
						{
							type: 'navigate',
							label: `Go to ${workspace?.title || workspace?.name || 'workspace'}`,
							action: 'navigate',
							data: {
								url: `/workspaces/${workspace?.id}`,
								workspace_id: workspace?.id,
								workspace_title: workspace?.title || workspace?.name
							}
						}
					]
				};
			} else {
				return {
					success: false,
					status: 'error',
					error: result.error
				};
			}
		} catch (error) {
			return {
				success: false,
				status: 'error',
				error: error instanceof Error ? error.message : 'Unknown error'
			};
		}
	}
});

/**
 * List workspaces
 */
export const listWorkspacesTool = createTool({
	id: 'list-workspaces',
	description: 'List workspaces with optional filtering by organization or title',
	outputSchema: workspaceOutputSchema.optional(),
	execute: async ({ runtimeContext }) => {
		try {
			// In newer Mastra versions, parameters are passed directly in context

			let path = '/api/v1/workspaces/';
			const params: string[] = [];

			// Try to get organization_id from multiple sources
			const orgId = runtimeContext.get('currentOrganizationId') as string;

			if (orgId) params.push(`organization_id=${orgId}`);
			if (params.length > 0) path += '?' + params.join('&');

			const result = await getJson<any>(path, runtimeContext.toJSON());

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);

				// Create UI actions for each workspace
				const ui_actions: any[] = [];
				// Check for workspaces in different possible locations
				let workspaces = null;
				if (normalized.data && normalized.data.items && Array.isArray(normalized.data.items)) {
					workspaces = normalized.data.items;
				} else if (normalized.workspaces && Array.isArray(normalized.workspaces)) {
					workspaces = normalized.workspaces;
				} else if (Array.isArray(normalized)) {
					workspaces = normalized;
				}

				if (workspaces && Array.isArray(workspaces)) {
					for (const workspace of workspaces) {
						ui_actions.push({
							type: 'navigate',
							label: `Go to ${workspace.title}`,
							action: 'navigate',
							data: {
								url: `/workspaces/${workspace.id}`,
								workspace_id: workspace.id,
								workspace_title: workspace.title
							}
						});
					}
				}

				return {
					success: true,
					message: 'Workspaces retrieved successfully',
					data: normalized,
					ui_actions: ui_actions
				};
			} else {
				return {
					success: false,
					message: `Error listing workspaces: ${result.error}`,
					data: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				message: `Error listing workspaces: ${error instanceof Error ? error.message : 'Unknown error'}`
			};
		}
	}
});

/**
 * Update workspace
 */
export const updateWorkspaceTool = createTool({
	id: 'update-workspace',
	description: 'Update an existing workspace by ID',
	inputSchema: z.object({
		workspace_id: z.number().describe('The ID of the workspace to update'),
		updates: z.record(z.any()).describe('Dictionary of fields to update')
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z.any().optional()
	}),
	execute: async ({ context, runtimeContext }) => {
		const { workspace_id, updates } = context;
		try {
			if (!updates || Object.keys(updates).length === 0) {
				return {
					success: false,
					message: 'Error: No updates provided'
				};
			}

			const result = await putJson<any>(
				`/api/v1/workspaces/${workspace_id}/`,
				updates,
				runtimeContext.toJSON()
			);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				return {
					success: true,
					message: 'Workspace updated successfully',
					data: normalized
				};
			} else {
				return {
					success: false,
					message: `Error updating workspace: ${result.error}`,
					data: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				message: `Error updating workspace: ${error instanceof Error ? error.message : 'Unknown error'}`
			};
		}
	}
});

/**
 * Delete workspace
 */
export const deleteWorkspaceTool = createTool({
	id: 'delete-workspace',
	description: 'Delete a workspace by ID',
	inputSchema: z.object({
		workspace_id: z.number().describe('The ID of the workspace to delete')
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z.any().optional()
	}),
	execute: async ({ context, runtimeContext }) => {
		const { workspace_id } = context;
		try {
			const result = await postJson<any>(
				`/api/v1/workspaces/${workspace_id}/delete/`,
				{},
				runtimeContext.toJSON()
			);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				return {
					success: true,
					message: 'Workspace deleted successfully',
					data: normalized
				};
			} else {
				return {
					success: false,
					message: `Error deleting workspace: ${result.error}`,
					data: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				message: `Error deleting workspace: ${error instanceof Error ? error.message : 'Unknown error'}`
			};
		}
	}
});

/**
 * Get workspace
 */
export const getWorkspaceTool = createTool({
	id: 'get-workspace',
	description: 'Get a specific workspace by ID',
	inputSchema: z.object({
		workspace_id: z.number().describe('The ID of the workspace to retrieve')
	}),
	outputSchema: z.object({
		success: z.boolean(),
		message: z.string(),
		data: z.any().optional()
	}),
	execute: async ({ context, runtimeContext }) => {
		const { workspace_id } = context;
		try {
			const result = await postJson<any>(
				`/api/v1/workspaces/${workspace_id}/get/`,
				{},
				runtimeContext.toJSON()
			);

			if (result.success) {
				const normalized = normalizeWorkspacePayload(result.data);
				return {
					success: true,
					message: 'Workspace retrieved successfully',
					data: normalized
				};
			} else {
				return {
					success: false,
					message: `Error retrieving workspace: ${result.error}`,
					data: result.data
				};
			}
		} catch (error) {
			return {
				success: false,
				message: `Error retrieving workspace: ${error instanceof Error ? error.message : 'Unknown error'}`
			};
		}
	}
});

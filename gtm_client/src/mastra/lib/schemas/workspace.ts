import { z } from 'zod';
import { userSchema } from './user';
import { uiActionsSchema } from './shared';

export const WorkspaceStatusSchema = z.enum(['deployed', 'archived', 'draft']);

export const workspaceSchema = z.object({
	id: z.number().optional(),
	title: z.string().optional(),
	description: z.string().optional(),
	owner: userSchema.optional(),
	status: WorkspaceStatusSchema.optional()
});

export const workspaceOutputSchema = z.object({
	success: z.boolean(),
	status: z.string(),
	workspace: z.any().optional(),
	error: z.string().optional(),
	ui_actions: uiActionsSchema.optional()
});

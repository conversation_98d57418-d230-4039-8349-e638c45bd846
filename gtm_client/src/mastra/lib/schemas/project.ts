import { z } from 'zod';

export const ProjectStatusSchema = z.enum(['deployed', 'archived', 'draft']);
export const projectSchema = z.object({
	id: z.number().optional(),
	name: z.string().optional(),
	status: ProjectStatusSchema.optional(),
	server_supported: z.boolean().optional(),
	subdomain: z
		.object({
			id: z.number().optional(),
			name: z.string().optional(),
			full_uri: z.string().optional(),
			domain_name: z.string().optional(),
			domain_id: z.number().optional()
		})
		.optional(),
	workspace_id: z.number().optional(),
	project_purposes: z
		.array(
			z.object({
				id: z.number().optional(),
				name: z.string().optional()
			})
		)
		.optional(),
	project_type_id: z.number().optional(),
	project_platform_id: z.number().optional()
});

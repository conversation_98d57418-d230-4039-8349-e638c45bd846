import { z } from 'zod';
import { userSchema } from './user';
import { organizationSchema } from './organization';
import { workspaceSchema } from './workspace';
import { projectSchema } from './project';

export const globalContextSchema = z.object({
	userId: z.union([z.string(), z.number()]).optional(),
	userEmail: z.string().optional(),
	userInformation: userSchema.optional(),
	currentOrganizationId: z.string().optional(),
	currentOrganizationInformation: organizationSchema.optional(),
	currentWorkspaceId: z.union([z.string(), z.number()]).optional(),
	currentWorkspaceInformation: workspaceSchema.optional(),
	currentProjectId: z.union([z.string(), z.number()]).optional(),
	currentProjectInformation: projectSchema.optional()
});

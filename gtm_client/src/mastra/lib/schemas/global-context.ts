import { z } from 'zod';
import { userSchema } from './user';
import { organizationSchema } from './organization';
import { workspaceSchema } from './workspace';
import { projectSchema } from './project';

export const globalContextSchema = z.object({
	userId: z.string().optional(),
	userEmail: z.string().optional(),
	userInformation: userSchema.optional(),
	currentOrganizationId: z.string().optional(),
	currentOrganizationInformation: organizationSchema.optional(),
	currentWorkspaceId: z.string().optional(),
	currentWorkspaceInformation: workspaceSchema.optional(),
	currentProjectId: z.string().optional(),
	currentProjectInformation: projectSchema.optional()
});

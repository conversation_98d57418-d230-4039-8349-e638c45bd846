/**
 * Environment variable utilities for Mastra
 *
 * This module provides a centralized way to access environment variables
 * that works correctly in both development and production environments.
 */

import { env } from '$env/dynamic/private';
import { createGoogleGenerativeAI } from '@ai-sdk/google';

/**
 * Get the Google API key from environment variables
 *
 * In SvelteKit, we use $env/dynamic/private to access environment variables
 * on the server side. This ensures the variables are loaded correctly.
 */
export function getGoogleApiKey(): string {
	return env.GOOGLE_GENERATIVE_AI_API_KEY || env.VITE_GOOGLE_API_KEY || '';
}

/**
 * Get the model name from environment variables
 */
export function getModelName(): string {
	return env.VITE_DEFAULT_MODEL || 'gemini-2.5-pro';
}

/**
 * Get a configured Google model instance with API key
 */
export function getModel() {
	const apiKey = getGoogleApiKey();

	if (!apiKey) {
		throw new Error(
			'Google API key is required. Please set GOOGLE_GENERATIVE_AI_API_KEY or VITE_GOOGLE_API_KEY environment variable.'
		);
	}

	// Create a Google provider instance with the API key
	const googleProvider = createGoogleGenerativeAI({
		apiKey: apiKey
	});

	// Return the model with the configured provider
	const modelName = getModelName();
	return googleProvider(modelName);
}

/**
 * Check if environment variables are properly configured
 */
export function checkEnvConfig(): { hasApiKey: boolean; hasModel: boolean; apiKeyPrefix?: string } {
	const apiKey = getGoogleApiKey();
	const modelName = getModelName();

	return {
		hasApiKey: !!apiKey,
		hasModel: !!modelName,
		apiKeyPrefix: apiKey ? apiKey.substring(0, 10) : undefined
	};
}

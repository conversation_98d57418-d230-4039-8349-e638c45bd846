/**
 * Environment variable utilities for Mastra
 *
 * This module provides a centralized way to access environment variables
 * that works correctly in both development and production environments.
 */

import { env } from '$env/dynamic/private';

/**
 * Get the Google API key from environment variables
 *
 * In SvelteKit, we use $env/dynamic/private to access environment variables
 * on the server side. This ensures the variables are loaded correctly.
 */
export function getGoogleApiKey(): string {
	return env.GOOGLE_GENERATIVE_AI_API_KEY || env.VITE_GOOGLE_API_KEY || '';
}

/**
 * Get the model name from environment variables
 */
export function getModel(): string {
	return env.VITE_DEFAULT_MODEL || 'google/gemini-2.5-pro-preview-06-05';
}

/**
 * Check if environment variables are properly configured
 */
export function checkEnvConfig(): { hasApiKey: boolean; hasModel: boolean; apiKeyPrefix?: string } {
	const apiKey = getGoogleApiKey();
	return {
		hasApiKey: !!apiKey,
		hasModel: !!getModel(),
		apiKeyPrefix: apiKey ? apiKey.substring(0, 10) : undefined
	};
}

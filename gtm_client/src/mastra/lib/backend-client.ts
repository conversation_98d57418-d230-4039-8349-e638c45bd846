/**
 * Backend Client for Mastra Tools
 *
 * This module provides utilities for Mastra tools to communicate with the Django backend API.
 * It handles authentication, headers, and HTTP requests.
 */

import { env } from '$env/dynamic/private';
import { dev } from '$app/environment';
import type { UserContext, BackendResponse } from './types';

const DJANGO_BASE_URL = env.API_URL || 'http://localhost:8000';

/**
 * Build headers for Django backend requests
 */
function buildHeaders(userContext?: UserContext): Record<string, string> {
	const headers: Record<string, string> = {
		'Content-Type': 'application/json',
		'X-Requested-With': 'XMLHttpRequest'
	};

	// Add API key in development mode
	if (dev) {
		headers['X-GTM-API-KEY'] = '1234';
	}

	if (userContext) {
		if (userContext?.sessionId) {
			headers['X-Session-ID'] = userContext.sessionId;
		}
	}

	return headers;
}

/**
 * POST JSON data to Django backend
 */
export async function postJson<T = any>(
	path: string,
	payload: Record<string, any>,
	userContext?: UserContext
): Promise<BackendResponse<T>> {
	const url = `${DJANGO_BASE_URL}${path}`;
	const headers = buildHeaders(userContext);

	try {
		const response = await fetch(url, {
			method: 'POST',
			headers,
			body: JSON.stringify(payload)
		});

		const status = response.status;

		if (status >= 400) {
			let error: any;
			try {
				error = await response.json();
			} catch {
				error = await response.text();
			}

			return {
				status,
				data: error,
				success: false,
				error: typeof error === 'string' ? error : error.detail || error.message || 'Request failed'
			};
		}

		let data: T;
		try {
			data = await response.json();
		} catch {
			data = (await response.text()) as any;
		}

		return {
			status,
			data,
			success: true
		};
	} catch (error) {
		return {
			status: 500,
			data: null as any,
			success: false,
			error: error instanceof Error ? error.message : 'Network error'
		};
	}
}

/**
 * GET JSON data from Django backend
 */
export async function getJson<T = any>(
	path: string,
	userContext?: UserContext
): Promise<BackendResponse<T>> {
	const url = `${DJANGO_BASE_URL}${path}`;
	const headers = buildHeaders(userContext);

	try {
		const response = await fetch(url, {
			method: 'GET',
			headers
		});

		const status = response.status;

		if (status >= 400) {
			let error: any;
			try {
				error = await response.json();
			} catch {
				error = await response.text();
			}

			return {
				status,
				data: error,
				success: false,
				error: typeof error === 'string' ? error : error.detail || error.message || 'Request failed'
			};
		}

		let data: T;
		try {
			data = await response.json();
		} catch {
			data = (await response.text()) as any;
		}

		return {
			status,
			data,
			success: true
		};
	} catch (error) {
		return {
			status: 500,
			data: null as any,
			success: false,
			error: error instanceof Error ? error.message : 'Network error'
		};
	}
}

/**
 * PUT JSON data to Django backend
 */
export async function putJson<T = any>(
	path: string,
	payload: Record<string, any>,
	userContext?: UserContext
): Promise<BackendResponse<T>> {
	const url = `${DJANGO_BASE_URL}${path}`;
	const headers = buildHeaders(userContext);

	try {
		const response = await fetch(url, {
			method: 'PUT',
			headers,
			body: JSON.stringify(payload)
		});

		const status = response.status;

		if (status >= 400) {
			let error: any;
			try {
				error = await response.json();
			} catch {
				error = await response.text();
			}

			return {
				status,
				data: error,
				success: false,
				error: typeof error === 'string' ? error : error.detail || error.message || 'Request failed'
			};
		}

		let data: T;
		try {
			data = await response.json();
		} catch {
			data = (await response.text()) as any;
		}

		return {
			status,
			data,
			success: true
		};
	} catch (error) {
		return {
			status: 500,
			data: null as any,
			success: false,
			error: error instanceof Error ? error.message : 'Network error'
		};
	}
}

/**
 * DELETE request to Django backend
 */
export async function deleteJson<T = any>(
	path: string,
	userContext?: UserContext
): Promise<BackendResponse<T>> {
	const url = `${DJANGO_BASE_URL}${path}`;
	const headers = buildHeaders(userContext);

	try {
		const response = await fetch(url, {
			method: 'DELETE',
			headers
		});

		const status = response.status;

		if (status >= 400) {
			let error: any;
			try {
				error = await response.json();
			} catch {
				error = await response.text();
			}

			return {
				status,
				data: error,
				success: false,
				error: typeof error === 'string' ? error : error.detail || error.message || 'Request failed'
			};
		}

		// DELETE might return empty response
		let data: T;
		try {
			data = await response.json();
		} catch {
			data = null as any;
		}

		return {
			status,
			data,
			success: true
		};
	} catch (error) {
		return {
			status: 500,
			data: null as any,
			success: false,
			error: error instanceof Error ? error.message : 'Network error'
		};
	}
}

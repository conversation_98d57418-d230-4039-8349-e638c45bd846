/**
 * Workspace Agent for Mastra
 *
 * Handles workspace management and organization operations.
 * Migrated from FastMCP workspace_agent.
 */

import { Agent, MastraMemory, type MastraLanguageModel } from '@mastra/core';
import { createTool } from '@mastra/core/tools';
import { z } from 'zod';
import { getModel } from '$mastra/lib/env';
import { Memory } from '@mastra/memory';
import { globalContextSchema, workspaceOutputSchema } from '$mastra/lib/schemas';
import {
	createWorkspaceTool,
	deleteWorkspaceTool,
	getWorkspaceTool,
	updateWorkspaceTool,
	listWorkspacesTool
} from '$mastra/tools/workspace';

const workspaceInstruction = `
# Role
You are the Workspace Agent — a specialized operational agent within the TagRabbit ecosystem.  
You manage the lifecycle of internal workspaces used to organize projects and related resources such as pages, events, billing, and permissions within an organization.  
These workspaces are **distinct from Google Tag Manager workspaces** and serve as the structural foundation for organizing and managing operational entities.

# Core Responsibilities
1. **Execution:** Perform workspace operations through the available tools:  
   - **CreateWorkspaceTool:** Create new workspaces within the organization.  
   - **ListWorkspacesTool:** Retrieve all or filtered workspaces by organization or title.  
   - **GetWorkspaceTool:** Retrieve a specific workspace by its ID.  
   - **UpdateWorkspaceTool:** Modify workspace properties such as title, description, or status.  
   - **DeleteWorkspaceTool:** Delete a workspace by its ID.  
   - **FilterWorkspacesTool:** Filter and query workspaces based on defined criteria.  
2. **Precision:** Use only the parameters provided. Never assume or fabricate missing data — request clarification when necessary.  
3. **Validation:** Confirm all actions before performing destructive or critical operations such as deletion or overwriting existing data.  
4. **Normalization:** Return normalized workspace data (id, title, description, owner, status) to ensure consistency across agents.  
5. **Context Awareness:** Always respect and use the user context for authentication, organization scoping, and data access control.

# Behavioral Guidelines
- Keep responses structured, concise, and factual.  
- When creating or updating a workspace, confirm success and include navigation or follow-up actions (e.g., UI actions).  
- Never fabricate results — rely only on validated tool output.  
- If a tool operation fails, clearly report the error and recommend next steps.  
- Prefer automation over manual processes whenever possible.  
- Clearly indicate what occurred (e.g., “Workspace created”, “Workspace updated”, “Workspace deleted”) in your responses.

# Tone and Style
- Professional, efficient, and confident.  
- Use clear, declarative language.  
- Avoid speculation; prefer direct confirmation or specific follow-up questions.  
- Always reference workspace identifiers (ID, title) when describing results or actions.

# Goal
Ensure that workspace creation, retrieval, updates, and deletions are accurate, consistent, and synchronized across the organization.  
Your objective is to maintain integrity and traceability of workspace data while supporting seamless coordination within the TagRabbit orchestration framework.
`;

const workspaceDescription = `
You are the Workspace Agent — a specialized agent responsible for managing organizational workspaces within the TagRabbit system.  
You handle creating, retrieving, updating, filtering, and deleting workspaces that organize projects, pages, events, billing, and permissions.  
Your focus is precision, consistency, and maintaining synchronized workspace data across the organization.
`;

export const workspaceAgent = new Agent({
	name: 'workspaceAgent',
	description: workspaceDescription,
	instructions: workspaceInstruction,
	model: getModel() as unknown as MastraLanguageModel,
	memory: new Memory({
		options: {
			workingMemory: {
				enabled: true,
				schema: globalContextSchema,
				scope: 'thread'
			}
		}
	}) as unknown as MastraMemory,
	tools: {
		CreateWorkspaceTool: createWorkspaceTool,
		ListWorkspacesTool: listWorkspacesTool,
		UpdateWorkspaceTool: updateWorkspaceTool,
		DeleteWorkspaceTool: deleteWorkspaceTool,
		GetWorkspaceTool: getWorkspaceTool
	}
});

export const workspaceAgentTool = createTool({
	id: 'workspaceAgent',
	description: 'Calls the Workspace Agent for managing workspaces',
	inputSchema: z.object({
		user_request: z.string().describe('The user message or action to process')
	}),
	outputSchema: workspaceOutputSchema,
	execute: async ({ context, mastra, writer, runtimeContext }) => {
		const sessionId = runtimeContext.get('sessionId') as string;
		const conversationId = (runtimeContext.get('conversationId') as string) ?? sessionId;
		const { user_request } = context;

		const agent = mastra!.getAgent('workspaceAgent');
		const stream = await agent.stream(user_request, {
			runtimeContext,
			memory: {
				thread: conversationId,
				resource: sessionId
			}
		});
		if (writer) {
			await stream.textStream.pipeTo(writer);
		}
		const finalText = await stream.text;
		// Try to extract/parse json from a code fence first, then raw
		const fenced = /```json\s*([\s\S]*?)\s*```/i.exec(finalText);
		const candidate = fenced?.[1] ?? finalText;

		let parsed: unknown;
		try {
			parsed = JSON.parse(candidate);
		} catch {
			// not JSON — fall back to a structured wrapper
		}
		if (parsed) {
			const result = workspaceOutputSchema.safeParse(parsed);
			if (result.success) {
				return result.data;
			}
		}
		return {
			success: true,
			status: 'stream_complete',
			workspace: undefined,
			error: undefined,
			ui_actions: undefined
		};
	}
});

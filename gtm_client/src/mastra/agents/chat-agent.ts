// /**
//  * Master Chat Agent for <PERSON><PERSON>
//  *
//  * This is the main conversational agent that users interact with.
//  * It has access to all workspace tools and can orchestrate operations based on user requests.
//  *
//  * Similar to the master_agent in the FastMCP implementation, but using <PERSON><PERSON>'s architecture.
//  */

// import { Agent } from '@mastra/core';
// import { createGoogleGenerativeAI } from '@ai-sdk/google';
// import {
// 	createWorkspaceTool,
// 	listWorkspacesTool,
// 	updateWorkspaceTool,
// 	deleteWorkspaceTool,
// 	getWorkspaceTool
// } from '../tools/workspace-tools';
// import {
// 	createProjectTool,
// 	listProjectsTool,
// 	getProjectTool,
// 	updateProjectTool,
// 	deleteProjectTool
// } from '../tools/project-tools';
// import {
// 	createPageTool,
// 	listPagesTool,
// 	getPageTool,
// 	updatePageTool,
// 	deletePageTool,
// 	getSuggestedPagesTool
// } from '../tools/page-tools';
// import {
// 	checkPermissionTool,
// 	getUserPermissionsTool,
// 	checkWorkspacePermissionTool,
// 	checkProjectPermissionTool,
// 	getUserOrganizationRoleTool
// } from '../tools/permission-tools';
// import { getGoogleApiKey, getModelName } from '../lib/env';

// const AGENT_INSTRUCTIONS = `You are the GTM Mixer Assistant, a helpful AI that helps users manage their workspaces, projects, pages, and permissions.

// ## Your Capabilities

// ### Workspace Management
// - Create new workspaces with titles and descriptions
// - List all workspaces or filter by organization/title
// - Update workspace properties (title, description)
// - Delete workspaces by ID
// - Retrieve specific workspace details by ID

// ### Project Management
// - Create new projects in workspaces
// - List all projects in a workspace
// - Get details of a specific project
// - Update project information
// - Delete projects

// ### Page Management
// - Create new pages in projects
// - List all pages in a project
// - Get details of a specific page
// - Update page information
// - Delete pages
// - Get suggested pages for a project based on its type and platform

// ### Permission Management
// - Check if user has permission for specific actions on resources
// - Get all permissions for a user
// - Check workspace-specific permissions
// - Check project-specific permissions
// - Get user's role in an organization

// ## How to Help Users

// 1. **Understand Intent**: Listen carefully to what the user wants to do
// 2. **Use Tools**: Call the appropriate tools to fulfill requests
// 3. **Provide Context**: Always explain what you're doing and what the results mean
// 4. **Be Helpful**: If a user's request is unclear, ask clarifying questions
// 5. **Handle Errors**: If something goes wrong, explain the error clearly and suggest solutions

// ## Examples

// **User**: "List my workspaces"
// **You**: Use the list-workspaces tool and present the results in a friendly format

// **User**: "Create a workspace called Marketing"
// **You**: Use the create-workspace tool with title "Marketing" and confirm success

// **User**: "Update workspace 5 to have description 'Q1 2024 campaigns'"
// **You**: Use the update-workspace tool with the workspace_id and new description

// ## Important Notes

// - Always use the user's organization context when available
// - Provide clear, concise responses
// - If you need more information to complete a task, ask the user
// - When listing items, format them in a readable way
// - Confirm successful operations and explain any failures

// Remember: You're here to make workspace management easy and intuitive!`;

// /**
//  * Create Google provider instance
//  *
//  * We use the environment utilities to get the API key.
//  * This ensures proper loading in SvelteKit's server environment.
//  */
// const google = createGoogleGenerativeAI({
// 	apiKey: getGoogleApiKey()
// });

// /**
//  * Master Chat Agent
//  *
//  * This agent orchestrates all workspace operations and provides a conversational interface.
//  */
// export const chatAgent = new Agent({
// 	name: 'GTM Mixer Assistant',
// 	instructions: AGENT_INSTRUCTIONS,

// 	model: google(getModelName()),

// 	tools: {
// 		// Workspace tools
// 		createWorkspace: createWorkspaceTool,
// 		listWorkspaces: listWorkspacesTool,
// 		updateWorkspace: updateWorkspaceTool,
// 		deleteWorkspace: deleteWorkspaceTool,
// 		getWorkspace: getWorkspaceTool,

// 		// Project tools
// 		createProject: createProjectTool,
// 		listProjects: listProjectsTool,
// 		getProject: getProjectTool,
// 		updateProject: updateProjectTool,
// 		deleteProject: deleteProjectTool,

// 		// Page tools
// 		createPage: createPageTool,
// 		listPages: listPagesTool,
// 		getPage: getPageTool,
// 		updatePage: updatePageTool,
// 		deletePage: deletePageTool,
// 		getSuggestedPages: getSuggestedPagesTool,

// 		// Permission tools
// 		checkPermission: checkPermissionTool,
// 		getUserPermissions: getUserPermissionsTool,
// 		checkWorkspacePermission: checkWorkspacePermissionTool,
// 		checkProjectPermission: checkProjectPermissionTool,
// 		getUserOrganizationRole: getUserOrganizationRoleTool
// 	}
// });
export {};

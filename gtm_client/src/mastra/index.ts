import { <PERSON><PERSON> } from '@mastra/core';
import { Memory } from '@mastra/memory';

import { LibSQLStore } from '@mastra/libsql';

// Import agents
import type { MastraStorage } from '@mastra/core';
import { tagRabbitAgent } from './agents/tag-rabbit-agent';
import { workspaceAgent } from './agents/workspace-agent';

export const mastra = new Mastra({
	storage: new LibSQLStore({
		url: 'file:mastra.db'
	}) as unknown as MastraStorage,
	agents: {
		tagRabbitAgent,
		workspaceAgent
	}
});

// Export utilities for use in tools
export * from './lib/types';
export * from './lib/backend-client';
export * from './lib/tool-helpers';

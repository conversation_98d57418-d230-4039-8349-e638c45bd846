import { <PERSON><PERSON> } from '@mastra/core';
import { Memory } from '@mastra/memory';

import { LibSQLStore } from '@mastra/libsql';

// Import agents
import { chatAgent } from './agents/chat-agent';

export const mastra = new Mastra({
	memory: new Memory({
		storage: new LibSQLStore({
			url: 'file:working-memory.db'
		}),
		options: {
			workingMemory: {
				enabled: true
			},
			threads: {
				generateTitle: true
			}
		}
	}),
});

// Export utilities for use in tools
export * from './lib/types';
export * from './lib/backend-client';
export * from './lib/tool-helpers';

import { <PERSON><PERSON> } from '@mastra/core';
import { Memory } from '@mastra/memory';

import { LibSQLStore } from '@mastra/libsql';

// Import agents
import { chatAgent } from './agents/chat-agent';
import type { MastraStorage } from '@mastra/core';

export const mastra = new Mastra({
	storage: new LibSQLStore({
		url: 'file:mastra.db'
	}) as unknown as MastraStorage
	
});

// Export utilities for use in tools
export * from './lib/types';
export * from './lib/backend-client';
export * from './lib/tool-helpers';

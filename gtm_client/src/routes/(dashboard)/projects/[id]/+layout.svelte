<script lang="ts">
	import { page } from '$app/state';
	import { globalUIContext } from '$lib/stores/globalContext.svelte';
	import { onD<PERSON>roy } from 'svelte';

	let { children } = $props();

	// Sets and updates the global context to be in sync with all the current changes
	$effect(() => {
		globalUIContext.update((context) => {
			return {
				...context,
				currentWorkspaceId: page.data.workspace.id,
				currentWorkspaceInformation: page.data.workspace,
				currentProjectId: page.data.project.id,
				currentProjectInformation: page.data.project,
				extra: {
					adNetworks: page.data.adNetworks,
					projectPurposes: page.data.projectPurposes,
					projectTypes: page.data.projectTypes
				}
			};
		});
	});

	onDestroy(() => {
		globalUIContext.update((context) => {
			return {
				...context,
				currentWorkspaceId: undefined,
				currentWorkspaceInformation: undefined,
				currentProjectId: undefined,
				currentProjectInformation: undefined,
				extra: {
					adNetworks: undefined,
					projectPurposes: undefined,
					projectTypes: undefined
				}
			};
		});
	});
</script>

{@render children()}

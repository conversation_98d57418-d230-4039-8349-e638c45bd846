import type { Workspace } from '$lib/interfaces/workspace';
import type { PageServerLoad } from './$types';
import { redirect } from '@sveltejs/kit';

import { createProjectSchema } from './formSchemas';
import { superValidate, type SuperValidated } from 'sveltekit-superforms';
import { zod, type Infer } from 'sveltekit-superforms/adapters';
import { callViaRouteName } from '$lib/server/repl';

export const actions = callViaRouteName([
	{
		name: 'create_project',
		method: 'POST',
		allowCookies: true,
		schema: createProjectSchema,
		namespace: 'project'
	}
]);

export const load: PageServerLoad = async ({ url, fetch, cookies, locals }) => {
	if (!locals.user) {
		redirect(302, '/');
	}
	const workspaceId = url.searchParams.get('workspaceId');

	if (!workspaceId) {
		cookies.set('message', 'Workspace required for route.', { path: '/' });
		cookies.set('messageType', 'error', { path: '/' });
		redirect(302, '/dashboard');
		return;
	}

	const getWorkspace = async () => {
		const relativePath = `/spark/api/v1/workspaces/${workspaceId}`;

		const absoluteUrl = new URL(relativePath, url.origin);
		return fetch(absoluteUrl)
			.then(async (res) => {
				if (!res.ok) {
					return null;
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? {};
			});
	};

	const workspace = (await getWorkspace()) as Workspace;

	if (!workspace) {
		cookies.set('message', 'Workspace not found', { path: '/' });
		cookies.set('messageType', 'error', { path: '/' });
		redirect(302, '/dashboard');
	}

	const getAllProjectPurposes = async () => {
		const relativePath = '/spark/api/v1/projects/project-purposes/';
		const absoluteUrl = new URL(relativePath, url.origin);
		return fetch(absoluteUrl)
			.then(async (res) => {
				if (!res.ok) {
					return null;
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? [];
			});
	};
	const getAllProjectTypes = async () => {
		const relativePath = '/spark/api/v1/projects/project-types/';
		const absoluteUrl = new URL(relativePath, url.origin);
		return fetch(absoluteUrl)
			.then(async (res) => {
				if (!res.ok) {
					return null;
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? [];
			});
	};

	const getAdNetworks = async () => {
		const relativePath = `/spark/api/v1/networks/ad-networks/`;
		return fetch(relativePath, { method: 'GET' })
			.then(async (res) => {
				if (!res.ok) {
					return null;
				} else {
					return res.json();
				}
			})
			.then((data) => {
				return data?.data ?? [];
			});
	};

	return {
		adNetworks: await getAdNetworks(),
		form: await superValidate(zod(createProjectSchema)),
		workspace,
		projectPurposes: await getAllProjectPurposes(),
		projectTypes: await getAllProjectTypes()
	};
};

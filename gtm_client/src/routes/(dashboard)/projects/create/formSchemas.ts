import { z } from 'zod';

const domainLabelSchema = z
	.string()
	.min(1, { message: 'Each part of the domain must be between 1 and 63 characters.' })
	.max(63, { message: 'Each part of the domain must be between 1 and 63 characters.' })
	.regex(/^[a-z0-9-]+$/, {
		// Enforces lowercase, numbers, and hyphens
		message: 'Domain parts can only use lowercase letters (a-z), numbers (0-9), and hyphens (-).'
	})
	.refine((label) => label !== '-', {
		message: 'A domain part cannot consist solely of a hyphen.'
	})
	.refine((label) => !label.startsWith('-') && !label.endsWith('-'), {
		message: 'Domain parts cannot start or end with a hyphen.'
	});

/**
 * Schema for a Fully Qualified Domain Name (FQDN), e.g., "example.com", "sub.example.co.uk".
 */
export const fqdnSchema = z
	.string()
	.min(3, { message: 'Domain name must be at least 3 characters long (e.g., a.co).' })
	.max(253, { message: 'Domain name cannot exceed 253 characters.' })
	.transform((val) => val.toLowerCase().trim())
	.superRefine((domain, ctx) => {
		// 1. Basic structural checks
		if (!domain.includes('.')) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Domain name must include at least one dot (e.g., example.com).'
			});
			return z.NEVER; // Halt further checks for this refine if structure is fundamentally wrong
		}
		if (domain.startsWith('.') || domain.endsWith('.')) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Domain name cannot start or end with a dot.'
			});
			return z.NEVER;
		}
		if (domain.includes('..')) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Domain name cannot contain consecutive dots (e.g., example..com).'
			});
			return z.NEVER;
		}

		// 2. Label-based checks
		const labels = domain.split('.');

		if (labels.length < 2) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Domain name must consist of at least two parts (e.g., example.com).'
			});
			return z.NEVER;
		}

		// Validate each label
		let allLabelsStructurallyValid = true;
		labels.forEach((label, index) => {
			const labelParseResult = domainLabelSchema.safeParse(label);
			if (!labelParseResult.success) {
				allLabelsStructurallyValid = false;
				labelParseResult.error.errors.forEach((err) => {
					ctx.addIssue({
						code: z.ZodIssueCode.custom,
						// Prepend which part of the domain failed
						message: `Issue with domain part "${label}" (part ${index + 1}/${labels.length}): ${err.message}`
					});
				});
			}
		});

		if (!allLabelsStructurallyValid) {
			return z.NEVER;
		}

		// 3. Specific TLD (last label) validation
		const tld = labels[labels.length - 1];

		if (tld.length < 2) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: `The Top-Level Domain (TLD part: "${tld}") must be at least 2 characters long.`
			});
		}

		// TLDs generally shouldn't be all numbers (e.g. "123" as a TLD is not standard)
		if (/^[0-9]+$/.test(tld)) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: `The Top-Level Domain (TLD part: "${tld}") cannot consist entirely of numbers.`
			});
		}
	});

// Subdomain name validation based on Django's DomainWithSubdomainsSerializer
const subdomainNameSchema = z
	.string()
	.min(1, { message: 'Subdomain name must be between 1 and 63 characters.' })
	.max(63, { message: 'Subdomain name must be between 1 and 63 characters.' })
	.regex(/^[a-z0-9-]+$/, {
		message: 'Subdomain name can only contain lowercase letters, numbers, and hyphens.'
	})
	.refine((name) => !['www', 'mail', 'ftp'].includes(name.toLowerCase()), {
		// Ensure case-insensitivity for reserved names
		message: 'Subdomain name cannot be a reserved name (e.g., www, mail, ftp).'
	})
	.refine((name) => name !== '-', { message: 'Subdomain name cannot be just a hyphen.' })
	.refine((name) => !name.startsWith('-') && !name.endsWith('-'), {
		message: 'Subdomain name cannot start or end with a hyphen.'
	});

export const createProjectSchema = z.object({
	name: z.string().min(1, { message: 'Name is required' }).max(255),
	// description: z.string().min(1, { message: 'Description is required' }).max(1000),
	id: z.string().optional(),
	subdomain: subdomainNameSchema,
	domain: fqdnSchema,
	is_server_side: z.boolean().default(true),
	workspace: z.number().int().positive(),
	/**
	 * Represents an array of ProjectPurpose IDs.
	 * Based on Django's `project_purpose_ids = PrimaryKeyRelatedField(many=True, required=False)`.
	 * `optional()` means the field can be omitted. If present, it can be an empty array.
	 * If at least one reason is required when the field is present, use `.nonempty()` inside the array or on the array itself.
	 */
	project_reasons: z
		.array(z.string().min(1, { message: 'Project reason ID cannot be empty.' }))
		.min(0, { message: 'At least one project reason is required.' })
		.default([]),
	// project_reasons: z
	// 	.array(z.string().min(1, { message: 'Project reason ID cannot be empty.' }))
	// 	.nonempty({ message: 'At least one project reason is required.' }),

	/**
	 * Represents the ProjectType ID.
	 * Based on Django's `project_type_id = PrimaryKeyRelatedField(write_only=True)`.
	 * Assumed to be a required string ID.
	 */
	project_type: z.string().min(1, { message: 'Project Type ID is required' }),

	/**
	 * Represents the ProjectPlatform ID.
	 * Based on Django's `project_platform_id = PrimaryKeyRelatedField(write_only=True)`.
	 * Assumed to be a required string ID.
	 */
	project_platform: z.string().min(1, { message: 'Project Platform ID is required' }),

	ad_networks: z.array(z.string().min(1, { message: 'Ad Network ID cannot be empty.' })).default([])
});
// .superRefine((data, ctx) => {
// 	// Complex cross-field validation, like the ProjectTypePlatformAssociation check in your Django serializer,
// 	// often requires external data (e.g., a list of valid type/platform combinations or a DB call).
// 	// This is typically handled server-side.
// 	// If you have a predefined list of valid combinations client-side, you could implement a check here.
// 	// For example:
// 	// const isValidCombination = checkValidProjectTypePlatform(data.projectType, data.projectPlatform);
// 	// if (!isValidCombination) {
// 	//   ctx.addIssue({
// 	//     code: z.ZodIssueCode.custom,
// 	//     message: "Invalid project type and platform combination.",
// 	//     path: ["projectType"], // Or a more general path
// 	//   });
// 	//   ctx.addIssue({
// 	//     code: z.ZodIssueCode.custom,
// 	//     message: "Invalid project type and platform combination.",
// 	//     path: ["projectPlatform"],
// 	//   });
// 	// }
// 	// Django's ProjectSerializer also validates that if domain_data is provided, subdomain_data must also be provided (and vice-versa).
// 	// In this Zod schema, `domain` and `subdomain` are individual string fields.
// 	// If they were optional and you wanted to enforce that if one is given, the other must also be given,
// 	// you could add a check like this:
// 	// if ((data.domain && !data.subdomain) || (!data.domain && data.subdomain)) {
// 	//   ctx.addIssue({
// 	//     code: z.ZodIssueCode.custom,
// 	//     message: "Both domain and subdomain must be provided if one is specified.",
// 	//     path: ["domain"], // Or a more general path
// 	//   });
// 	// }
// 	// However, as `domain` and `subdomain` are currently defined as required strings, this specific check isn't needed
// 	// unless you make them optional.
// });

export type CreateProjectSchema = z.infer<typeof createProjectSchema>;

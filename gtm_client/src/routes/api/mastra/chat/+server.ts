/**
 * Mastra Chat Agent API Endpoint
 *
 * This is the main orchestrator endpoint for the Mastra chat agent.
 * The chat agent has access to all tools (workspace, project, page, permission)
 * and can handle conversational requests across all domains.
 *
 * Supports both streaming and non-streaming responses.
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { mastra } from '$mastra';
import { RuntimeContext } from '@mastra/core/runtime-context';
import type { GlobalContext } from '$lib/stores/globalContext.svelte';

/**
 * Extract UI actions from tool call results
 */
function extractUIActions(toolCalls: any[], toolResults: any[]): any[] {
	const uiActions: any[] = [];

	// First check tool results (this is where <PERSON><PERSON> puts the actual responses)
	if (toolResults) {
		for (const toolResult of toolResults) {
			// Check for UI actions in the tool result payload
			if (toolResult.payload?.result?.ui_actions) {
				uiActions.push(...toolResult.payload.result.ui_actions);
			}
			// Also check direct result
			if (toolResult.result?.ui_actions) {
				uiActions.push(...toolResult.result.ui_actions);
			}
		}
	}

	// Fallback: check tool calls (legacy support)
	if (toolCalls && uiActions.length === 0) {
		for (const toolCall of toolCalls) {
			// Check multiple possible locations for UI actions
			if (toolCall.output?.ui_actions) {
				uiActions.push(...toolCall.output.ui_actions);
			} else if (toolCall.result?.ui_actions) {
				uiActions.push(...toolCall.result.ui_actions);
			}
		}
	}

	return uiActions;
}

export const POST: RequestHandler = async ({ request }) => {
	try {
		const { message, messages = [], stream = false, context } = await request.json();
		const headerSessionId = request.headers.get('X-Session-ID');
		const runtimeContext = new RuntimeContext<
			GlobalContext & { conversationId?: string; sessionId?: string }
		>();

		const sessionId = String(context?.sessionId || headerSessionId || 'anonymous');

		runtimeContext.set('conversationId', context?.conversationId);
		runtimeContext.set('sessionId', sessionId);
		runtimeContext.set('currentOrganizationId', context?.currentOrganizationId);
		runtimeContext.set('currentWorkspaceId', context?.currentWorkspaceId);
		runtimeContext.set('currentProjectId', context?.currentProjectId);

		// Get the chat agent (orchestrator)
		const agent = mastra.getAgent('tagRabbitAgent');

		if (!agent) {
			return json(
				{
					success: false,
					error: 'Chat agent not found'
				},
				{ status: 500 }
			);
		}

		// Validate message
		if (!message) {
			return json(
				{
					success: false,
					error: 'Message is required'
				},
				{ status: 400 }
			);
		}

		// Format conversation history and current message for the agent
		let contextualMessage = `User context: ${JSON.stringify(context)}\n\n`;

		// Add conversation history if available
		if (messages.length > 1) {
			contextualMessage += `Conversation history:\n`;
			// Include all messages except the last one (which is the current message)
			const historyMessages = messages.slice(0, -1);
			for (const msg of historyMessages) {
				const role = msg.role === 'user' ? 'User' : 'Assistant';
				contextualMessage += `${role}: ${msg.content}\n`;
			}
			contextualMessage += `\nCurrent user message: ${message}`;
		} else {
			contextualMessage += `User message: ${message}`;
		}

		// Handle streaming response
		// if (stream) {
		// 	const encoder = new TextEncoder();
		// 	const readable = new ReadableStream({
		// 		async start(controller) {
		// 			try {
		// 				const result = await agent.stream(contextualMessage, {
		// 					onStepFinish: (step) => {
		// 						// console.log('Step finished:', step);
		// 						// Send step updates to client
		// 						const data = JSON.stringify({
		// 							type: 'step',
		// 							step: {
		// 								text: step.text,
		// 								toolCalls: step.toolCalls,
		// 								finishReason: step.finishReason
		// 							}
		// 						});
		// 						controller.enqueue(encoder.encode(`data: ${data}\n\n`));
		// 					}
		// 				});

		// 				// Send final result
		// 				const uiActions = {};
		// 				// extractUIActions(result.toolCalls || [], result.toolResults || []);
		// 				const finalData = JSON.stringify({
		// 					type: 'done',
		// 					result: {
		// 						text: result.text,
		// 						toolCalls: result.toolCalls,
		// 						uiActions: uiActions
		// 					}
		// 				});
		// 				controller.enqueue(encoder.encode(`data: ${finalData}\n\n`));
		// 				controller.close();
		// 			} catch (error) {
		// 				console.error('Error in agent stream', { error, runId: (error as any).runId });
		// 				const errorData = JSON.stringify({
		// 					type: 'error',
		// 					error: error instanceof Error ? error.message : 'Unknown error'
		// 				});
		// 				controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
		// 				controller.close();
		// 			}
		// 		}
		// 	});

		// 	return new Response(readable, {
		// 		headers: {
		// 			'Content-Type': 'text/event-stream',
		// 			'Cache-Control': 'no-cache',
		// 			Connection: 'keep-alive'
		// 		}
		// 	});
		// }
		if (stream) {
			const encoder = new TextEncoder();
			const readable = new ReadableStream({
				async start(controller) {
					let closed = false;
					let currentText = '';
					let toolCalls: any[] = [];
					let toolResults: any[] = [];

					const safeEnqueue = (obj: any) => {
						if (closed) return;
						controller.enqueue(encoder.encode(`data: ${JSON.stringify(obj)}\n\n`));
					};

					try {
						const result = await agent.stream(contextualMessage, {
							memory: {
								thread: sessionId,
								resource: sessionId
							},
							onStepFinish: (step: any) => {
								// Accumulate text
								if (step.text) {
									currentText += step.text;
								}

								// Track tool calls and results
								if (step.toolCalls) {
									toolCalls.push(...step.toolCalls);
								}
								if (step.toolResults) {
									toolResults.push(...step.toolResults);
								}

								// Send step update in expected format
								safeEnqueue({
									type: 'step',
									step: {
										text: step.text || '',
										toolCalls: step.toolCalls || [],
										finishReason: step.finishReason
									}
								});
							}
						});

						// Extract UI actions from tool results
						const uiActions = extractUIActions(toolCalls, toolResults);

						// Send final result in expected format
						safeEnqueue({
							type: 'done',
							result: {
								text: result.text || currentText,
								toolCalls: toolCalls,
								uiActions: uiActions
							}
						});

						closed = true;
						controller.close();
					} catch (err) {
						console.error('Error in agent stream', { error: err });
						safeEnqueue({
							type: 'error',
							error: err instanceof Error ? err.message : 'Unknown error'
						});
						closed = true;
						controller.close();
					}
				}
			});

			return new Response(readable, {
				headers: {
					'Content-Type': 'text/event-stream',
					'Cache-Control': 'no-cache',
					Connection: 'keep-alive'
				}
			});
		}

		// Handle non-streaming response
		let toolCalls: any[] = [];
		let toolResults: any[] = [];

		const result = await agent.stream(contextualMessage, {
			memory: {
				thread: sessionId,
				resource: sessionId
			},
			onStepFinish: (step: any) => {
				// Track tool calls and results
				if (step.toolCalls) {
					toolCalls.push(...step.toolCalls);
				}
				if (step.toolResults) {
					toolResults.push(...step.toolResults);
				}
			}
		});

		// Extract UI actions from tool results
		const uiActions = extractUIActions(toolCalls, toolResults);

		return json({
			success: true,
			result: result.text,
			toolCalls: toolCalls,
			uiActions: uiActions
		});
	} catch (error) {
		console.error('Chat agent error:', error);
		return json(
			{
				success: false,
				error: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};

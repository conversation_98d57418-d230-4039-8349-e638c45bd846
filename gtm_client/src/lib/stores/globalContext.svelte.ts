import type { Organization } from '$lib/api/organizations';
import type { Project } from '$lib/interfaces/project';
import type { PublicUser } from '$lib/interfaces/user';
import type { Workspace } from '$lib/interfaces/workspace';
import { writable } from 'svelte/store';

export type UIVisualStates = {
	currentOrganizationId?: string | number;
	currentOrganizationInformation?: Organization;
	currentWorkspaceId?: string | number;
	currentWorkspaceInformation?: Workspace;
	currentProjectId?: string | number;
	currentProjectInformation?: Project;
};

export type GlobalContext = {
	userId?: string | number;
	userEmail?: string;
	userInformation?: PublicUser;
} & UIVisualStates & {
		extra?: Record<string, any>;
	};

export const globalUIContext = writable<GlobalContext>({});

type GlobalContextChanges = {
	userId?: string | number;
	userEmail?: string;
	userInformation?: PublicUser;
	uiVisualStates?: Partial<UIVisualStates>;
};

import { dev } from '$app/environment';
import { parse } from 'cookie';

export interface ChatResponse {
	content: Array<{ type: string; text: string }>;
	structuredContent: {
		result: string;
		suggested_domain?: string;
		available_domains?: string[];
		user_context?: any;
	};
	isError: boolean;
	conversation_id?: string;
	message_id?: string;
	actions?: any[];
	context_updates?: Record<string, any>;
	success?: boolean;
	suggestions?: string[];
	response?: string;
	error?: string;
}

export class AIAgentService {
	private baseUrl: string | null = null;

	/**
	 * Initialize the service by fetching configuration from the server
	 */
	private async initializeConfig(): Promise<void> {
		if (this.baseUrl) return; // Already initialized

		try {
			const response = await fetch('/api/config');
			if (!response.ok) {
				throw new Error(`Failed to fetch config: ${response.status}`);
			}
			const config = await response.json();
			this.baseUrl = config.mcpServerUrl;
		} catch (error) {
			console.error('Failed to load configuration, using fallback:', error);
			this.baseUrl = 'http://localhost:8006'; // Fallback
		}
	}

	/**
	 * Build headers for MCP server requests, including user authentication
	 */
	private buildMcpHeaders(context: Record<string, any>): Record<string, string> {
		const headers: Record<string, string> = {
			'Content-Type': 'application/json',
			Accept: 'application/json'
		};

		const cookies = parse(document.cookie);

		if (cookies?.sessionid) {
			headers['X-Session-ID'] = cookies.sessionid;
		}

		return headers;
	}

	/**
	 * Send a chat message with streaming support.
	 * @param message The user's message.
	 * @param context The user's context.
	 * @param onChunk Callback for streaming chunks.
	 * @param conversationId The optional conversation ID.
	 */
	async sendMessage(
		message: string,
		context: Record<string, any>,
		onChunk?: (chunk: string) => void,
		conversationId?: string
	): Promise<ChatResponse> {
		try {
			// Initialize configuration if needed
			await this.initializeConfig();

			// If onChunk is provided, use streaming
			if (onChunk) {
				return await this.sendMessageStreaming(message, context, onChunk, conversationId);
			}

			// Build headers with user authentication
			const headers = this.buildMcpHeaders(context);
			headers['Accept'] = 'application/json';

			// Otherwise use regular JSON response
			const response = await fetch(`${this.baseUrl}/mcp/chat`, {
				method: 'POST',
				headers,
				body: JSON.stringify({ message, context, conversationId })
			});

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				throw new Error(
					errorData.detail || errorData.error || `Request failed with status ${response.status}`
				);
			}

			const data = await response.json();

			// Transform the MCP response to match the expected format
			const chatResponse: ChatResponse = {
				content: data.content || [
					{ type: 'text', text: data.structuredContent?.result || 'No response' }
				],
				structuredContent: data.structuredContent || { result: 'No response' },
				isError: data.isError || false,
				response: data.content?.[0]?.text || data.structuredContent?.result || 'No response',
				success: !data.isError,
				conversation_id: data.conversation_id,
				actions: data.actions || [],
				context_updates: data.context_updates || {},
				suggestions: data.suggestions || []
			};

			return chatResponse;
		} catch (error) {
			console.error('AI Agent chat error:', error);
			throw error;
		}
	}

	/**
	 * Send a chat message with streaming support.
	 */
	private async sendMessageStreaming(
		message: string,
		context: Record<string, any>,
		onChunk: (chunk: string) => void,
		conversationId?: string
	): Promise<ChatResponse> {
		try {
			// Initialize configuration if needed
			await this.initializeConfig();

			// Build headers with user authentication
			const headers = this.buildMcpHeaders(context);
			headers['Accept'] = 'text/event-stream';

			const response = await fetch(`${this.baseUrl}/mcp/chat`, {
				method: 'POST',
				headers,
				body: JSON.stringify({ message, context, conversationId })
			});

			if (!response.ok || !response.body) {
				const errorData = await response.json().catch(() => ({}));
				throw new Error(
					errorData.detail || errorData.error || `Request failed with status ${response.status}`
				);
			}

			// Get the reader and decoder for the stream
			const reader = response.body.getReader();
			const decoder = new TextDecoder();
			let finalResult: ChatResponse | null = null;

			// Loop to read chunks from the stream
			while (true) {
				const { done, value } = await reader.read();
				if (done) {
					break;
				}

				const chunk = decoder.decode(value);
				const lines = chunk.split('\n');

				for (const line of lines) {
					if (line.startsWith('data: ')) {
						const data = line.slice(6);
						if (data.trim()) {
							try {
								const parsed = JSON.parse(data);

								if (parsed.type === 'status') {
									onChunk(parsed.message);
								} else if (parsed.type === 'result') {
									// Transform the result to match expected format
									const result = parsed.data;
									const responseText =
										result.content?.[0]?.text || result.structuredContent?.result || 'No response';
									finalResult = {
										content: result.content || [
											{ type: 'text', text: result.structuredContent?.result || 'No response' }
										],
										structuredContent: result.structuredContent || { result: 'No response' },
										isError: result.isError || false,
										response: responseText,
										success: !result.isError,
										conversation_id: result.conversation_id,
										actions: result.actions || [],
										context_updates: result.context_updates || {},
										suggestions: result.suggestions || []
									};
									onChunk(responseText);
								} else if (parsed.type === 'error') {
									const errorResult = parsed.data;
									const errorText = errorResult.content?.[0]?.text || 'Error occurred';
									finalResult = {
										content: errorResult.content || [{ type: 'text', text: 'Error occurred' }],
										structuredContent: errorResult.structuredContent || {
											result: 'Error occurred'
										},
										isError: true,
										response: errorText,
										success: false,
										actions: [],
										context_updates: {},
										suggestions: []
									};
									onChunk(errorText);
								}
							} catch (e) {
								console.warn('Failed to parse streaming data:', data);
							}
						}
					}
				}
			}

			return (
				finalResult || {
					content: [{ type: 'text', text: 'No response received' }],
					structuredContent: { result: 'No response received' },
					isError: true,
					response: 'No response received',
					success: false,
					actions: [],
					context_updates: {},
					suggestions: []
				}
			);
		} catch (error) {
			console.error('AI Agent streaming error:', error);
			throw error;
		}
	}

	/**
	 * Get conversations from localStorage (dev mode) or backend
	 */
	private getDevConversations(): any[] {
		if (typeof localStorage === 'undefined') {
			return [];
		}
		try {
			const stored = localStorage.getItem('dev-conversations');
			return stored ? JSON.parse(stored) : [];
		} catch {
			return [];
		}
	}

	/**
	 * Save conversations to localStorage (dev mode)
	 */
	private saveDevConversations(conversations: any[]): void {
		if (typeof localStorage === 'undefined') {
			return;
		}
		try {
			localStorage.setItem('dev-conversations', JSON.stringify(conversations));
		} catch {
			// Ignore storage errors
		}
	}

	/**
	 * Get conversations from localStorage in dev mode or backend in production
	 */
	async getConversations(): Promise<any[]> {
		try {
			// In development mode, prioritize localStorage
			if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
				const localConversations = this.getDevConversations();
				if (localConversations.length > 0) {
					return localConversations;
				}
			}

			// Initialize configuration if needed
			await this.initializeConfig();

			const response = await fetch('/api/v1/conversations/', {
				method: 'GET',
				headers: {
					'Content-Type': 'application/json'
				}
			});

			if (!response.ok) {
				throw new Error(`Failed to get conversations: ${response.status}`);
			}

			const data = await response.json();
			const conversations = data.results || data;

			// If API returns empty but we have localStorage conversations, use those
			if (conversations.length === 0) {
				return this.getDevConversations();
			}

			return conversations;
		} catch (error) {
			console.error('Failed to get conversations:', error);
			// Fallback to localStorage in development
			return this.getDevConversations();
		}
	}

	/**
	 * Delete conversation
	 */
	async deleteConversation(conversationId: string): Promise<void> {
		try {
			const response = await fetch(`/api/v1/conversations/${conversationId}/`, {
				method: 'DELETE',
				headers: {
					'Content-Type': 'application/json'
				}
			});

			if (!response.ok) {
				throw new Error(`Failed to delete conversation: ${response.status}`);
			}
		} catch (error) {
			console.error('Failed to delete conversation:', error);
			throw error;
		}
	}

	/**
	 * Create a new conversation
	 */
	async createConversation(title?: string): Promise<string | null> {
		try {
			const response = await fetch('/api/v1/conversations/', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ title })
			});

			if (!response.ok) {
				throw new Error(`Failed to create conversation: ${response.status}`);
			}

			const data = await response.json();

			// In development mode, also save to localStorage
			if (data.id && data.id.startsWith('dev-conv-')) {
				const conversations = this.getDevConversations();
				const existingIndex = conversations.findIndex((c) => c.id === data.id);
				if (existingIndex === -1) {
					conversations.unshift(data);
					this.saveDevConversations(conversations);
				}
			}

			return data.id;
		} catch (error) {
			console.error('Failed to create conversation:', error);
			// Fallback: create a local conversation in development
			const localConversation = {
				id: `dev-conv-${Date.now()}`,
				title: title || 'New Conversation',
				created_at: new Date().toISOString(),
				updated_at: new Date().toISOString(),
				user: 1,
				message_count: 0
			};

			const conversations = this.getDevConversations();
			conversations.unshift(localConversation);
			this.saveDevConversations(conversations);

			return localConversation.id;
		}
	}

	/**
	 * Check AI agent health status
	 */
	async checkHealth(): Promise<any> {
		try {
			// Initialize configuration if needed
			await this.initializeConfig();

			const response = await fetch(`${this.baseUrl}/mcp/chat`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json'
				},
				body: JSON.stringify({ message: 'health check', context: {} })
			});

			if (!response.ok) {
				throw new Error(`Health check failed with status ${response.status}`);
			}

			const data = await response.json();
			return {
				status: 'healthy',
				timestamp: new Date().toISOString(),
				response: data
			};
		} catch (error) {
			console.error('AI Agent health check error:', error);
			throw error;
		}
	}
}

// Export singleton instance
export const aiAgentService = new AIAgentService();

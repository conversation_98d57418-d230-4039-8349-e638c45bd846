<script lang="ts">
	import { onMount } from 'svelte';

	// Icons
	import Bot from '@lucide/svelte/icons/bot';
	import User from '@lucide/svelte/icons/user';
	import Send from '@lucide/svelte/icons/send';
	import Loader2 from '@lucide/svelte/icons/loader-2';
	import AlertCircle from '@lucide/svelte/icons/alert-circle';
	import { globalUIContext } from '$lib/stores/globalContext.svelte';

	let {
		initialMessage = '',
		showHeader = true
	}: {
		initialMessage?: string;
		showHeader?: boolean;
	} = $props();

	let messages = $state<
		Array<{ role: 'user' | 'assistant'; content: string; toolCalls?: any[]; uiActions?: any[] }>
	>([]);
	let input = $state('');
	let loading = $state(false);
	let error = $state<string | null>(null);
	let streamingMessage = $state('');
	let isStreaming = $state(false);
	let isConnected = $state(true);
	let chatContainer: HTMLElement;

	let pageContext = $derived($globalUIContext);

	$effect(() => {
		console.log('Page context changed:', pageContext);
	});

	async function sendMessage(messageText: string, useStreaming = true) {
		if (!messageText.trim()) return;

		messages.push({ role: 'user', content: messageText });
		input = '';
		loading = true;
		error = null;
		streamingMessage = '';
		isStreaming = useStreaming;

		try {
			if (useStreaming) {
				const response = await fetch('/api/mastra/chat', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						message: messageText,
						messages: messages,
						stream: true,
						context: pageContext
					})
				});

				if (!response.ok) {
					throw new Error(`HTTP ${response.status}: ${response.statusText}`);
				}

				const reader = response.body?.getReader();
				const decoder = new TextDecoder();

				if (!reader) {
					throw new Error('No response body');
				}

				let buffer = '';

				while (true) {
					const { done, value } = await reader.read();
					if (done) break;

					buffer += decoder.decode(value, { stream: true });
					const lines = buffer.split('\n');
					buffer = lines.pop() || '';

					for (const line of lines) {
						if (line.startsWith('data: ')) {
							try {
								const data = JSON.parse(line.slice(6));

								if (data.type === 'step') {
									streamingMessage += data.step.text || '';
								} else if (data.type === 'done') {
									messages.push({
										role: 'assistant',
										content: data.result.text,
										toolCalls: data.result.toolCalls,
										uiActions: data.result.uiActions
									});
									streamingMessage = '';
									isStreaming = false;
								} else if (data.type === 'error') {
									throw new Error(data.error);
								}
							} catch (parseError) {
								console.error('Failed to parse streaming data:', parseError);
							}
						}
					}
				}
			} else {
				const response = await fetch('/api/mastra/chat', {
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						message: messageText,
						messages: messages,
						stream: false,
						context: pageContext
					})
				});

				const data = await response.json();

				if (!data.success) {
					throw new Error(data.error || 'Request failed');
				}

				messages.push({
					role: 'assistant',
					content: data.result,
					toolCalls: data.toolCalls,
					uiActions: data.uiActions
				});
			}
		} catch (err) {
			error = err instanceof Error ? err.message : 'Unknown error';
			console.error('Chat error:', err);
		} finally {
			loading = false;
		}
	}

	function handleSubmit(e: Event) {
		e.preventDefault();
		if (input.trim() && !loading) {
			sendMessage(input);
		}
	}

	function handleUIAction(action: any) {
		if (action.type === 'navigate' && action.data?.url) {
			window.location.href = action.data.url;
		}
	}

	function scrollToBottom() {
		if (chatContainer) {
			chatContainer.scrollTop = chatContainer.scrollHeight;
		}
	}

	$effect(() => {
		if (messages.length > 0) {
			setTimeout(scrollToBottom, 100);
		}
	});

	onMount(() => {
		if (initialMessage) {
			sendMessage(initialMessage);
		}
	});
</script>

<div class="flex h-full flex-col bg-slate-50 dark:bg-slate-900 {showHeader ? 'border-l' : ''}">
	{#if showHeader}
		<div
			class="flex items-center justify-between border-b border-slate-200 bg-white p-4 shadow-sm dark:border-slate-700 dark:bg-slate-800"
		>
			<div class="flex items-center gap-3">
				<div
					class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900"
				>
					<Bot class="h-5 w-5 text-blue-600 dark:text-blue-400" />
				</div>
				<h3 class="font-semibold text-slate-800 dark:text-slate-200">GTM Mixer Assistant</h3>
			</div>
			{#if isConnected}
				<div class="flex items-center gap-1.5">
					<div class="h-2 w-2 rounded-full bg-green-500"></div>
					<span class="text-xs text-slate-500 dark:text-slate-400">Online</span>
				</div>
			{:else}
				<div class="flex items-center gap-1.5">
					<div class="h-2 w-2 rounded-full bg-red-500"></div>
					<span class="text-xs text-slate-500 dark:text-slate-400">Offline</span>
				</div>
			{/if}
		</div>
	{/if}

	{#if pageContext && Object.keys(pageContext).length > 0}
		<div
			class="border-b border-slate-200 bg-slate-100 px-4 py-2 text-xs text-slate-500 dark:border-slate-700 dark:bg-slate-800/50 dark:text-slate-400"
		>
			<span class="font-medium">Context:</span>
			User: {pageContext.userEmail || 'N/A'} | Org: {pageContext.currentOrganizationId || 'N/A'}
		</div>
	{/if}

	<div bind:this={chatContainer} class="flex-1 space-y-6 overflow-y-auto p-6">
		{#each messages as message, index (index)}
			<div class="flex items-start gap-4 {message.role === 'user' ? 'flex-row-reverse' : ''}">
				{#if message.role === 'assistant'}
					<div
						class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-slate-200 dark:bg-slate-600"
					>
						<Bot class="h-5 w-5 text-slate-600 dark:text-slate-300" />
					</div>
				{:else}
					<div
						class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-slate-200 dark:bg-slate-600"
					>
						<User class="h-5 w-5 text-slate-600 dark:text-slate-300" />
					</div>
				{/if}

				<div class="max-w-[80%]">
					<div
						class="rounded-lg p-4
                        {message.role === 'user'
							? 'rounded-br-none bg-blue-600 text-white'
							: 'rounded-bl-none bg-white text-slate-700 dark:bg-slate-700 dark:text-slate-200'}"
					>
						<p class="text-sm whitespace-pre-wrap">{message.content}</p>

						{#if message.toolCalls && message.toolCalls.length > 0}
							<div class="mt-3 space-y-2 border-t border-white/20 pt-3 dark:border-slate-600/50">
								{#each message.toolCalls as toolCall, toolIndex (toolIndex)}
									<div
										class="flex items-center gap-2 rounded bg-slate-200/50 px-2 py-1 text-xs text-slate-600 dark:bg-slate-600/50 dark:text-slate-300"
									>
										{#if toolCall.result}
											<span class="text-green-500">✓</span>
										{:else if toolCall.error}
											<span class="text-red-500">✗</span>
										{/if}
										<span class="font-medium">{toolCall.toolName}</span>
									</div>
								{/each}
							</div>
						{/if}

						{#if message.uiActions && message.uiActions.length > 0}
							<div
								class="mt-3 flex flex-wrap gap-2 border-t border-white/20 pt-3 dark:border-slate-600/50"
							>
								{#each message.uiActions as action, actionIndex (actionIndex)}
									<button
										class="rounded-md bg-blue-500 px-3 py-1.5 text-xs font-medium text-white transition-colors hover:bg-blue-600 focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:outline-none dark:bg-blue-500 dark:hover:bg-blue-600"
										onclick={() => handleUIAction(action)}
									>
										{action.label}
									</button>
								{/each}
							</div>
						{/if}
					</div>
				</div>
			</div>
		{/each}

		{#if isStreaming && streamingMessage}
			<div class="flex items-start gap-4">
				<div
					class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-slate-200 dark:bg-slate-600"
				>
					<Bot class="h-5 w-5 text-slate-600 dark:text-slate-300" />
				</div>
				<div
					class="max-w-[80%] rounded-lg rounded-bl-none bg-white p-4 text-slate-700 dark:bg-slate-700 dark:text-slate-200"
				>
					<p class="text-sm whitespace-pre-wrap">
						{streamingMessage}<span class="animate-pulse">▌</span>
					</p>
				</div>
			</div>
		{/if}

		{#if loading && !isStreaming}
			<div class="flex items-start gap-4">
				<div
					class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-slate-200 dark:bg-slate-600"
				>
					<Bot class="h-5 w-5 text-slate-600 dark:text-slate-300" />
				</div>
				<div
					class="max-w-[80%] rounded-lg rounded-bl-none bg-white p-4 text-slate-700 dark:bg-slate-700 dark:text-slate-200"
				>
					<div class="flex items-center gap-2">
						<Loader2 class="h-4 w-4 animate-spin text-slate-500 dark:text-slate-400" />
						<span class="text-sm text-slate-500 dark:text-slate-400">Thinking...</span>
					</div>
				</div>
			</div>
		{/if}

		{#if error}
			<div class="flex items-start gap-4">
				<div
					class="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/50"
				>
					<Bot class="h-5 w-5 text-red-600 dark:text-red-400" />
				</div>
				<div
					class="max-w-[80%] rounded-lg border border-red-200 bg-red-50 p-4 dark:border-red-800 dark:bg-red-900/20"
				>
					<div class="flex items-center gap-2 text-red-600 dark:text-red-400">
						<AlertCircle class="h-4 w-4 flex-shrink-0" />
						<span class="text-sm font-medium">{error}</span>
					</div>
				</div>
			</div>
		{/if}
	</div>

	<div class="border-t border-slate-200 bg-white p-4 dark:border-slate-700 dark:bg-slate-800">
		<form onsubmit={handleSubmit}>
			<div
				class="relative rounded-lg border border-slate-300 bg-white transition-all duration-200 focus-within:ring-2 focus-within:ring-blue-500 dark:border-slate-600 dark:bg-slate-700"
			>
				<textarea
					bind:value={input}
					placeholder="Ask anything..."
					class="max-h-48 min-h-[2.5rem] w-full resize-none border-none bg-transparent py-2 pr-12 pl-4 text-sm text-slate-900 placeholder:text-slate-400 focus:ring-0 focus:outline-none dark:text-white dark:placeholder:text-slate-500"
					disabled={loading}
					rows="1"
					oninput={(e) => {
						const target = e.target as HTMLTextAreaElement;
						target.style.height = 'auto';
						target.style.height = `${Math.min(target.scrollHeight, 192)}px`;
					}}
					onkeydown={(e) => {
						if (e.key === 'Enter' && !e.shiftKey) {
							e.preventDefault();
							if (input.trim() && !loading) {
								sendMessage(input);
							}
						}
					}}
				></textarea>
				<div class="absolute inset-y-0 right-0 flex items-center pr-3">
					<button
						type="submit"
						disabled={!input.trim() || loading || !isConnected}
						class="inline-flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-white transition-colors hover:bg-blue-700 disabled:cursor-not-allowed disabled:bg-slate-400 dark:disabled:bg-slate-500"
					>
						{#if loading}
							<Loader2 class="h-4 w-4 animate-spin" />
						{:else}
							<Send class="h-4 w-4" />
						{/if}
					</button>
				</div>
			</div>
		</form>
		<div class="px-2 pt-2 text-center text-xs text-slate-500 dark:text-slate-400">
			<kbd class="font-sans">Shift</kbd> + <kbd class="font-sans">Enter</kbd> for new line
		</div>

		{#if !isConnected}
			<p class="mt-2 text-center text-xs text-red-500">AI Assistant connection unavailable.</p>
		{/if}
	</div>
</div>

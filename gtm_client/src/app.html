<!doctype html>
<html lang="%lang%">
	<head>
		<meta charset="utf-8" />

		<link rel="icon" href="%sveltekit.assets%/favicon/favicon.ico" type="image/x-icon" />

		<link rel="apple-touch-icon" sizes="57x57" href="%sveltekit.assets%/apple-icon-57x57.png" />
		<link rel="apple-touch-icon" sizes="60x60" href="%sveltekit.assets%/apple-icon-60x60.png" />
		<link rel="apple-touch-icon" sizes="72x72" href="%sveltekit.assets%/apple-icon-72x72.png" />
		<link rel="apple-touch-icon" sizes="76x76" href="%sveltekit.assets%/apple-icon-76x76.png" />
		<link rel="apple-touch-icon" sizes="114x114" href="%sveltekit.assets%/apple-icon-114x114.png" />
		<link rel="apple-touch-icon" sizes="120x120" href="%sveltekit.assets%/apple-icon-120x120.png" />
		<link rel="apple-touch-icon" sizes="144x144" href="%sveltekit.assets%/apple-icon-144x144.png" />
		<link rel="apple-touch-icon" sizes="152x152" href="%sveltekit.assets%/apple-icon-152x152.png" />
		<link rel="apple-touch-icon" sizes="180x180" href="%sveltekit.assets%/apple-icon-180x180.png" />

		<link
			rel="icon"
			type="image/png"
			sizes="192x192"
			href="%sveltekit.assets%/android-icon-192x192.png"
		/>
		<link rel="icon" type="image/png" sizes="32x32" href="%sveltekit.assets%/favicon-32x32.png" />
		<link rel="icon" type="image/png" sizes="96x96" href="%sveltekit.assets%/favicon-96x96.png" />
		<link rel="icon" type="image/png" sizes="16x16" href="%sveltekit.assets%/favicon-16x16.png" />

		<link rel="manifest" href="%sveltekit.assets%/manifest.json" />

		<meta name="msapplication-TileColor" content="#ffffff" />
		<meta name="msapplication-TileImage" content="%sveltekit.assets%/ms-icon-144x144.png" />
		<meta name="theme-color" content="#ffffff" />

		<meta
			name="description"
			content="This SaaS platform revolutionises the way entrepreneurs with websites, funnels (leads or sales), or online shops set up tracking using Google Tag Manager (GTM)."
		/>

		<meta name="viewport" content="width=device-width, initial-scale=1" />

		<!-- Disable Mastra telemetry warning -->
		<script>
			globalThis.___MASTRA_TELEMETRY___ = true;
		</script>

		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>
	</body>
</html>

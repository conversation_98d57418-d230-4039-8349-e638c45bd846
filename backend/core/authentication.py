"""
DRF Authentication classes to replace Djapy authentication
"""

import logging

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.sessions.models import Session
from django.utils import timezone
from rest_framework.authentication import SessionAuthentication
from rest_framework.permissions import BasePermission

logger = logging.getLogger(__name__)
User = get_user_model()


class GTMSessionAuthentication(SessionAuthentication):
    """
    Custom session authentication that adds workspace context
    This replaces the GTMAuth functionality from Djapy
    """

    def authenticate(self, request):
        """
        Authenticate the request and add workspace context
        """
        # Check for API key authentication in development mode
        if settings.DEBUG:
            api_key_user = self.authenticate_via_api_key(request)
            if api_key_user:
                self.add_workspace_context(request, api_key_user)
                return (api_key_user, None)

        # Check for session ID in header (for proxied requests)
        header_session_user = self.authenticate_via_session_header(request)
        if header_session_user:
            self.add_workspace_context(request, header_session_user)
            return (header_session_user, None)

        # Check if Django already authenticated the user
        django_user = getattr(request._request, "user", None)
        if django_user and django_user.is_authenticated:
            self.add_workspace_context(request, django_user)
            return (django_user, None)

        # Fallback to standard session authentication
        user_auth_tuple = super().authenticate(request)

        if user_auth_tuple is None:
            return None

        user, auth = user_auth_tuple

        # Add workspace context to request (implement based on your GTMAuth logic)
        self.add_workspace_context(request, user)

        return user_auth_tuple

    def authenticate_via_api_key(self, request):
        """
        Authenticate using X-GTM-API-KEY header in development mode
        """
        # Only allow API key authentication in DEBUG mode
        if not settings.DEBUG:
            return None

        api_key_header_name = getattr(settings, "ADMIN_API_KEY_HEADER_NAME", "X-GTM-API-KEY")
        meta_header_key = f"HTTP_{api_key_header_name.upper().replace('-', '_')}"

        api_key_from_request = request.META.get(meta_header_key)

        if not api_key_from_request:
            return None

        expected_api_key = getattr(settings, "ADMIN_API_KEY", None)
        gtm_user_username = getattr(settings, "GTM_CREDENTIAL_USER_USERNAME", None)

        if not expected_api_key or not gtm_user_username:
            logger.warning(
                "GTMSessionAuthentication: ADMIN_API_KEY or GTM_CREDENTIAL_USER_USERNAME is not configured. "
                "API key authentication will be skipped."
            )
            return None

        if api_key_from_request == expected_api_key:
            try:
                user = User.objects.get(username=gtm_user_username)
                logger.info(
                    f"GTMSessionAuthentication: Authenticated user '{gtm_user_username}' via API key (DEBUG mode)."
                )
                return user
            except User.DoesNotExist:
                logger.error(
                    f"GTMSessionAuthentication: API key is valid, but the configured GTM user "
                    f"'{gtm_user_username}' was not found in the database."
                )
                return None
            except Exception as e:
                logger.exception(
                    f"GTMSessionAuthentication: Error fetching GTM user '{gtm_user_username}' "
                    f"during API key authentication: {e}"
                )
                return None

        return None

    def authenticate_via_session_header(self, request):
        """
        Authenticate using session ID from X-Session-ID header
        This allows proxied requests to authenticate using session ID in headers
        """
        session_header_name = getattr(settings, "SESSION_ID_HEADER_NAME", "X-Session-ID")
        meta_header_key = f"HTTP_{session_header_name.upper().replace('-', '_')}"

        session_id_from_header = request.META.get(meta_header_key)

        if not session_id_from_header:
            return None

        try:
            # Get the session from the database
            session = Session.objects.get(session_key=session_id_from_header)

            # Check if session is expired
            if session.expire_date < timezone.now():
                logger.warning(f"GTMSessionAuthentication: Session {session_id_from_header} has expired")
                return None

            # Decode session data to get user ID
            session_data = session.get_decoded()
            user_id = session_data.get("_auth_user_id")

            if not user_id:
                logger.warning(f"GTMSessionAuthentication: No user ID found in session {session_id_from_header}")
                return None

            # Get the user
            user = User.objects.get(pk=user_id)

            if not user.is_active:
                logger.warning(f"GTMSessionAuthentication: User {user_id} is not active")
                return None

            logger.info(f"GTMSessionAuthentication: Authenticated user '{user.username}' via session header")
            return user

        except Session.DoesNotExist:
            logger.warning(f"GTMSessionAuthentication: Session {session_id_from_header} not found")
            return None
        except User.DoesNotExist:
            logger.error(f"GTMSessionAuthentication: User {user_id} not found for session {session_id_from_header}")
            return None
        except Exception as e:
            logger.exception(f"GTMSessionAuthentication: Error authenticating via session header: {e}")
            return None

    def add_workspace_context(self, request, user):
        """
        Add workspace context to the request
        This should implement the same logic as your original GTMAuth class
        """
        # TODO: Implement workspace detection logic based on your existing GTMAuth
        # For now, we'll set it to None - you'll need to implement this based on
        # how your GTMAuth class determines the workspace
        request.workspace = None

        # Example implementation (adjust based on your actual logic):
        # workspace_id = request.headers.get('X-Workspace-ID')
        # if workspace_id:
        #     try:
        #         from workspaces.models import Workspace
        #         workspace = Workspace.objects.get(id=workspace_id, members=user)
        #         request.workspace = workspace
        #     except Workspace.DoesNotExist:
        #         request.workspace = None


class IsWorkspaceMember(BasePermission):
    """
    Permission class to check if user is a workspace member
    """

    def has_permission(self, request, view):
        """
        Check if user has workspace permission
        """
        if not request.user or not request.user.is_authenticated:
            return False

        # Check if workspace is available on request
        workspace = getattr(request, "workspace", None)
        return workspace is not None


class IsWorkspaceOwner(BasePermission):
    """
    Permission class to check if user is a workspace owner
    """

    def has_permission(self, request, view):
        """
        Check if user is workspace owner
        """
        if not request.user or not request.user.is_authenticated:
            return False

        workspace = getattr(request, "workspace", None)
        if not workspace:
            return False

        # Implement owner check based on your workspace model
        return hasattr(workspace, "owner") and workspace.owner == request.user


class IsOrganizationMember(BasePermission):
    """
    Permission class to check if user is an organization member
    """

    def has_permission(self, request, view):
        """
        Check if user is organization member
        """
        if not request.user or not request.user.is_authenticated:
            return False

        # Get organization from URL parameters or request
        organization_id = view.kwargs.get("organization_id")
        if not organization_id:
            return False

        # Check if user is member of the organization
        try:
            from organizations.models import Organization

            organization = Organization.objects.get(id=organization_id)
            return organization.members.filter(user=request.user, is_active=True).exists()
        except Organization.DoesNotExist:
            return False


class HasOrganizationPermission(BasePermission):
    """
    Permission class to check specific organization permissions
    """

    def has_permission(self, request, view):
        """
        Check if user has required organization permission
        """
        if not request.user or not request.user.is_authenticated:
            return False

        organization_id = view.kwargs.get("organization_id")
        if not organization_id:
            return False

        # Get required permission from view
        required_permission = getattr(view, "required_permission", "can_view")

        try:
            from organizations.models import Organization

            organization = Organization.objects.get(id=organization_id)
            member = organization.members.filter(user=request.user, is_active=True).first()

            if not member:
                return False

            # Check specific permission
            return getattr(member, required_permission, False)
        except Organization.DoesNotExist:
            return False

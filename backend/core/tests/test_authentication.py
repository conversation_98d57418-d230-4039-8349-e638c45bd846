"""
Tests for GTMSessionAuthentication class
"""

from datetime import timed<PERSON><PERSON>
from unittest.mock import patch

from core.authentication import GTMSessionAuthentication
from django.contrib.auth import get_user_model
from django.contrib.sessions.models import Session
from django.test import RequestFactory, TestCase, override_settings
from django.utils import timezone
from rest_framework.request import Request

User = get_user_model()


class GTMSessionAuthenticationTest(TestCase):
    """Test cases for GTMSessionAuthentication"""

    def setUp(self):
        """Set up test fixtures"""
        self.auth_instance = GTMSessionAuthentication()
        self.factory = RequestFactory()
        self.api_key_user = User.objects.create_user(
            username="ayomipo",  # This matches GTM_CREDENTIAL_USER_USERNAME
            email="<EMAIL>",
            password="testpass123",
        )

    @override_settings(
        DEBUG=True, ADMIN_API_KEY="test-key-123", GTM_CREDENTIAL_USER_USERNAME="ayomipo"
    )
    def test_api_key_authentication_in_debug_mode(self):
        """Test that API key authentication works in DEBUG mode"""
        # Create request with API key header
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should return user and auth token
        self.assertIsNotNone(result)
        user, auth = result
        self.assertEqual(user, self.api_key_user)
        self.assertIsNone(auth)  # We return None for auth token

    @override_settings(
        DEBUG=True, ADMIN_API_KEY="test-key-123", GTM_CREDENTIAL_USER_USERNAME="ayomipo"
    )
    def test_api_key_authentication_wrong_key(self):
        """Test that wrong API key doesn't authenticate"""
        # Create request with wrong API key
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="wrong-key")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should return None (no authentication)
        self.assertIsNone(result)

    @override_settings(
        DEBUG=False,
        ADMIN_API_KEY="test-key-123",
        GTM_CREDENTIAL_USER_USERNAME="ayomipo",
    )
    def test_api_key_authentication_disabled_in_production(self):
        """Test that API key authentication is disabled when DEBUG=False"""
        # Create request with correct API key
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should return None because DEBUG=False
        self.assertIsNone(result)

    @override_settings(
        DEBUG=True,
        ADMIN_API_KEY="test-key-123",
        GTM_CREDENTIAL_USER_USERNAME="nonexistent",
    )
    def test_api_key_authentication_user_not_found(self):
        """Test behavior when configured user doesn't exist"""
        # Create request with correct API key
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should return None because user doesn't exist
        self.assertIsNone(result)

    @override_settings(
        DEBUG=True, GTM_CREDENTIAL_USER_USERNAME="ayomipo"
    )  # Missing ADMIN_API_KEY
    def test_api_key_authentication_missing_settings(self):
        """Test behavior when required settings are missing"""
        # Create request with API key
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should return None because ADMIN_API_KEY is not configured
        self.assertIsNone(result)

    @override_settings(
        DEBUG=True, ADMIN_API_KEY="test-key-123", GTM_CREDENTIAL_USER_USERNAME="ayomipo"
    )
    def test_api_key_authentication_no_header(self):
        """Test behavior when no API key header is provided"""
        # Create request without API key header
        request = self.factory.get("/test/")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should return None because no API key header
        self.assertIsNone(result)

    @override_settings(
        DEBUG=True,
        ADMIN_API_KEY="test-key-123",
        GTM_CREDENTIAL_USER_USERNAME="ayomipo",
        ADMIN_API_KEY_HEADER_NAME="X-CUSTOM-API-KEY",
    )
    def test_custom_api_key_header_name(self):
        """Test that custom API key header name works"""
        # Create request with custom header name
        request = self.factory.get("/test/", HTTP_X_CUSTOM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should authenticate successfully
        self.assertIsNotNone(result)
        user, auth = result
        self.assertEqual(user, self.api_key_user)

    @override_settings(
        DEBUG=True, ADMIN_API_KEY="test-key-123", GTM_CREDENTIAL_USER_USERNAME="ayomipo"
    )
    def test_authenticate_via_api_key_method_directly(self):
        """Test the authenticate_via_api_key method directly"""
        # Create request with API key
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Call method directly
        result = self.auth_instance.authenticate_via_api_key(drf_request)

        # Should return the user
        self.assertEqual(result, self.api_key_user)

    @override_settings(
        DEBUG=True, ADMIN_API_KEY="test-key-123", GTM_CREDENTIAL_USER_USERNAME="ayomipo"
    )
    @patch.object(GTMSessionAuthentication, "add_workspace_context")
    def test_add_workspace_context_called(self, mock_add_workspace):
        """Test that add_workspace_context is called when API key authentication succeeds"""
        # Create request with API key
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        # Authenticate
        result = self.auth_instance.authenticate(drf_request)

        # Should call add_workspace_context
        self.assertIsNotNone(result)
        mock_add_workspace.assert_called_once_with(drf_request, self.api_key_user)


class SecurityTest(TestCase):
    """Test security aspects of the authentication system"""

    def setUp(self):
        """Set up test fixtures"""
        self.auth_instance = GTMSessionAuthentication()
        self.factory = RequestFactory()
        self.api_key_user = User.objects.create_user(
            username="ayomipo", email="<EMAIL>", password="testpass123"
        )

    def test_api_key_authentication_only_in_debug(self):
        """Comprehensive test that API key auth is completely disabled in production"""
        # Test with DEBUG=False (production mode)
        with override_settings(
            DEBUG=False,
            ADMIN_API_KEY="test-key-123",
            GTM_CREDENTIAL_USER_USERNAME="ayomipo",
        ):
            request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
            drf_request = Request(request)

            # Should not authenticate via API key
            result = self.auth_instance.authenticate_via_api_key(drf_request)
            self.assertIsNone(result)

    @override_settings(
        DEBUG=True, ADMIN_API_KEY="", GTM_CREDENTIAL_USER_USERNAME="ayomipo"
    )
    def test_empty_api_key_setting_disables_auth(self):
        """Test that empty ADMIN_API_KEY setting disables API key authentication"""
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="any-key")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_api_key(drf_request)
        self.assertIsNone(result)

    @override_settings(
        DEBUG=True, ADMIN_API_KEY="test-key-123", GTM_CREDENTIAL_USER_USERNAME=""
    )
    def test_empty_username_setting_disables_auth(self):
        """Test that empty GTM_CREDENTIAL_USER_USERNAME setting disables API key authentication"""
        request = self.factory.get("/test/", HTTP_X_GTM_API_KEY="test-key-123")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_api_key(drf_request)
        self.assertIsNone(result)


class SessionHeaderAuthenticationTest(TestCase):
    """Test cases for session header authentication functionality"""

    def setUp(self):
        """Set up test data"""
        self.auth_instance = GTMSessionAuthentication()
        self.factory = RequestFactory()

        # Create test user
        self.user = User.objects.create_user(
            username="testuser", email="<EMAIL>", password="testpass123"
        )

        # Create test session
        self.session = Session.objects.create(
            session_key="test_session_key_123",
            session_data=Session.objects.encode(
                {
                    "_auth_user_id": str(self.user.pk),
                    "_auth_user_backend": "django.contrib.auth.backends.ModelBackend",
                }
            ),
            expire_date=timezone.now() + timedelta(days=1),
        )

    def test_authenticate_via_session_header_success(self):
        """Test successful authentication via session header"""
        request = self.factory.get("/", HTTP_X_SESSION_ID="test_session_key_123")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_session_header(drf_request)

        self.assertEqual(result, self.user)

    def test_authenticate_via_session_header_no_header(self):
        """Test authentication when no session header is provided"""
        request = self.factory.get("/")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_session_header(drf_request)

        self.assertIsNone(result)

    def test_authenticate_via_session_header_invalid_session(self):
        """Test authentication with invalid session ID"""
        request = self.factory.get("/", HTTP_X_SESSION_ID="invalid_session_key")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_session_header(drf_request)

        self.assertIsNone(result)

    def test_authenticate_via_session_header_expired_session(self):
        """Test authentication with expired session"""
        # Create expired session
        expired_session = Session.objects.create(
            session_key="expired_session_key",
            session_data=Session.objects.encode(
                {
                    "_auth_user_id": str(self.user.pk),
                    "_auth_user_backend": "django.contrib.auth.backends.ModelBackend",
                }
            ),
            expire_date=timezone.now() - timedelta(days=1),  # Expired
        )

        request = self.factory.get("/", HTTP_X_SESSION_ID="expired_session_key")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_session_header(drf_request)

        self.assertIsNone(result)

    def test_authenticate_via_session_header_no_user_in_session(self):
        """Test authentication when session has no user ID"""
        # Create session without user ID
        no_user_session = Session.objects.create(
            session_key="no_user_session_key",
            session_data=Session.objects.encode({"some_other_data": "value"}),
            expire_date=timezone.now() + timedelta(days=1),
        )

        request = self.factory.get("/", HTTP_X_SESSION_ID="no_user_session_key")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_session_header(drf_request)

        self.assertIsNone(result)

    def test_authenticate_via_session_header_inactive_user(self):
        """Test authentication with inactive user"""
        # Create inactive user
        inactive_user = User.objects.create_user(
            username="inactive_user",
            email="<EMAIL>",
            password="testpass123",
            is_active=False,
        )

        # Create session for inactive user
        inactive_session = Session.objects.create(
            session_key="inactive_user_session",
            session_data=Session.objects.encode(
                {
                    "_auth_user_id": str(inactive_user.pk),
                    "_auth_user_backend": "django.contrib.auth.backends.ModelBackend",
                }
            ),
            expire_date=timezone.now() + timedelta(days=1),
        )

        request = self.factory.get("/", HTTP_X_SESSION_ID="inactive_user_session")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_session_header(drf_request)

        self.assertIsNone(result)

    @override_settings(SESSION_ID_HEADER_NAME="X-Custom-Session-ID")
    def test_authenticate_via_session_header_custom_header_name(self):
        """Test authentication with custom header name"""
        request = self.factory.get("/", HTTP_X_CUSTOM_SESSION_ID="test_session_key_123")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_session_header(drf_request)

        self.assertEqual(result, self.user)

    def test_authenticate_via_session_header_nonexistent_user(self):
        """Test authentication when user in session doesn't exist"""
        # Create session with non-existent user ID
        nonexistent_session = Session.objects.create(
            session_key="nonexistent_user_session",
            session_data=Session.objects.encode(
                {
                    "_auth_user_id": "99999",  # Non-existent user ID
                    "_auth_user_backend": "django.contrib.auth.backends.ModelBackend",
                }
            ),
            expire_date=timezone.now() + timedelta(days=1),
        )

        request = self.factory.get("/", HTTP_X_SESSION_ID="nonexistent_user_session")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_session_header(drf_request)

        self.assertIsNone(result)

    @override_settings(
        DEBUG=True,
        ADMIN_API_KEY="test_api_key",
        GTM_CREDENTIAL_USER_USERNAME="testuser",
    )
    def test_authenticate_integration_api_key_priority(self):
        """Test that API key authentication has priority over session header"""
        request = self.factory.get(
            "/",
            HTTP_X_GTM_API_KEY="test_api_key",
            HTTP_X_SESSION_ID="test_session_key_123",
        )
        drf_request = Request(request)

        result = self.auth_instance.authenticate(drf_request)

        self.assertIsNotNone(result)
        self.assertEqual(result[0], self.user)

    def test_authenticate_integration_session_header_priority(self):
        """Test that session header authentication works in full authenticate method"""
        request = self.factory.get("/", HTTP_X_SESSION_ID="test_session_key_123")
        drf_request = Request(request)

        result = self.auth_instance.authenticate(drf_request)

        self.assertIsNotNone(result)
        self.assertEqual(result[0], self.user)

    @patch("core.authentication.logger")
    def test_authenticate_via_session_header_logging(self, mock_logger):
        """Test that appropriate logging occurs"""
        request = self.factory.get("/", HTTP_X_SESSION_ID="test_session_key_123")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_session_header(drf_request)

        self.assertEqual(result, self.user)
        mock_logger.info.assert_called_once()
        self.assertIn("Authenticated user", mock_logger.info.call_args[0][0])

    @patch("core.authentication.logger")
    def test_authenticate_via_session_header_logging_expired(self, mock_logger):
        """Test logging for expired session"""
        # Create expired session
        expired_session = Session.objects.create(
            session_key="expired_for_logging",
            session_data=Session.objects.encode(
                {
                    "_auth_user_id": str(self.user.pk),
                    "_auth_user_backend": "django.contrib.auth.backends.ModelBackend",
                }
            ),
            expire_date=timezone.now() - timedelta(days=1),
        )

        request = self.factory.get("/", HTTP_X_SESSION_ID="expired_for_logging")
        drf_request = Request(request)

        result = self.auth_instance.authenticate_via_session_header(drf_request)

        self.assertIsNone(result)
        mock_logger.warning.assert_called_once()
        self.assertIn("has expired", mock_logger.warning.call_args[0][0])
